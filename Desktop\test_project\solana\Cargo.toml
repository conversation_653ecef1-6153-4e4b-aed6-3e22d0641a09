[package]
name = "pump-fun-monitor"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = "0.20"
tungstenite = "0.20"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.11", features = ["json"] }
futures-util = "0.3"
log = "0.4"
env_logger = "0.10"
anyhow = "1.0"
thiserror = "1.0"
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
solana-client = "1.18"
solana-sdk = "1.18"
solana-rpc-client-api = "1.18"
solana-account-decoder = "1.18"
base64 = "0.21"
bs58 = "0.5"
borsh = "0.10"
clap = { version = "4.0", features = ["derive"] }
dotenv = "0.15"
solana-transaction-status = "1.18"
solana-program = "1.18"
spl-token = "4.0"
spl-associated-token-account = "2.3"
hex = "0.4"

[dev-dependencies]
tokio-test = "0.4"
